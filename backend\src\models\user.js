'use strict';
const { Model } = require('sequelize');
const bcrypt = require('bcrypt');

module.exports = (sequelize, DataTypes) => {
    class User extends Model {
        static associate(models) {
            User.belongsTo(models.Role, { foreignKey: 'role_id' });
            User.hasMany(models.Course, { foreignKey: 'user_id' });
            User.belongsToMany(models.Course, { through: models.StudentCourse, foreignKey: 'user_id' });
            User.hasMany(models.QuizResult, { foreignKey: 'user_id' });
            User.hasMany(models.CourseResult, { foreignKey: 'user_id' });
        }

        // Ph<PERSON>ơng thức để so sánh mật khẩu
        async comparePassword(password) {
            return await bcrypt.compare(password, this.password);
        }
    }

    User.init(
        {
            user_id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true,
                autoIncrementIdentity: true
            },
            name: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            email: {
                type: DataTypes.STRING,
                allowNull: false,
                unique: true,
            },
            password: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            role_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'Roles',
                    key: 'role_id',
                },
            },
        },
        {
            sequelize,
            modelName: 'User',
            tableName: 'Users',
            timestamps: false, // Nếu không cần createdAt và updatedAt
            hooks: {
                // Hook mã hóa mật khẩu trước khi tạo người dùng
                beforeCreate: async (user) => {
                    if (user.password) {
                        const salt = await bcrypt.genSalt(10); // Tạo salt với độ phức tạp 10
                        user.password = await bcrypt.hash(user.password, salt); // Mã hóa mật khẩu
                    }
                },
                // Hook mã hóa mật khẩu trước khi cập nhật người dùng (nếu mật khẩu thay đổi)
                beforeUpdate: async (user) => {
                    if (user.password && user.changed('password')) {
                        const salt = await bcrypt.genSalt(10);
                        user.password = await bcrypt.hash(user.password, salt);
                    }
                },
            },
        }
    );

    return User;
};