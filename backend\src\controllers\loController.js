const { LO, Chapter, Question, ChapterLO, Subject } = require('../models');
const { Op } = require('sequelize');
//const { sequelize } = require('../config/database');

exports.getAllLOs = async (req, res) => {
    try {
        const { page = 1, limit = 10 } = req.query;
        const offset = (page - 1) * limit;

        const los = await LO.findAndCountAll({
            limit: parseInt(limit),
            offset: parseInt(offset),
            include: [
                {
                    model: Chapter,
                    as: 'Chapters',
                    attributes: ['chapter_id', 'name'],
                    through: { attributes: [] },
                },
            ],
        });

        res.status(200).json({
            totalItems: los.count,
            totalPages: Math.ceil(los.count / limit),
            currentPage: parseInt(page),
            los: los.rows,
        });
    } catch (error) {
        console.error('Lỗi khi lấy danh sách LO:', error);
        res.status(500).json({ message: 'Lỗi khi lấy danh sách LO', error: error.message });
    }
};

exports.getLOById = async (req, res) => {
    try {
        const { id } = req.params;
        if (!id) {
            return res.status(400).json({ message: 'Thiếu ID của LO' });
        }

        const lo = await LO.findByPk(id, {
            include: [
                {
                    model: Chapter,
                    as: 'Chapters',
                    attributes: ['chapter_id', 'name'],
                    through: { attributes: [] }
                },
                {
                    model: Question,
                    attributes: ['question_id', 'question_text']
                }
            ],
        });

        if (!lo) {
            return res.status(404).json({ message: 'Không tìm thấy LO' });
        }

        res.status(200).json(lo);
    } catch (error) {
        console.error('Lỗi khi lấy thông tin LO:', error);
        res.status(500).json({ message: 'Lỗi server', error: error.message });
    }
};

exports.createLO = async (req, res) => {
    const transaction = await sequelize.transaction();
    try {
        const { name, description } = req.body;

        if (!name) {
            await transaction.rollback();
            return res.status(400).json({ message: 'Tên LO là bắt buộc' });
        }

        // Kiểm tra trùng lặp tên LO
        const existingLO = await LO.findOne({
            where: { name },
            transaction
        });

        if (existingLO) {
            await transaction.rollback();
            return res.status(400).json({ message: 'Tên LO đã tồn tại' });
        }

        const newLO = await LO.create({
            name,
            description: description || null
        }, { transaction });

        await transaction.commit();
        res.status(201).json(newLO);
    } catch (error) {
        await transaction.rollback();
        console.error('Lỗi khi tạo LO:', error);
        res.status(500).json({ message: 'Lỗi server', error: error.message });
    }
};

exports.updateLO = async (req, res) => {
    const transaction = await sequelize.transaction();
    try {
        const { id } = req.params;
        const { name, description } = req.body;

        if (!id) {
            await transaction.rollback();
            return res.status(400).json({ message: 'Thiếu ID của LO' });
        }

        const lo = await LO.findByPk(id, { transaction });
        if (!lo) {
            await transaction.rollback();
            return res.status(404).json({ message: 'Không tìm thấy LO' });
        }

        if (name) {
            // Kiểm tra trùng lặp tên LO
            const existingLO = await LO.findOne({
                where: {
                    name,
                    lo_id: { [Op.ne]: id }
                },
                transaction
            });

            if (existingLO) {
                await transaction.rollback();
                return res.status(400).json({ message: 'Tên LO đã tồn tại' });
            }
        }

        await lo.update({
            name: name || lo.name,
            description: description || lo.description
        }, { transaction });

        await transaction.commit();
        res.status(200).json(lo);
    } catch (error) {
        await transaction.rollback();
        console.error('Lỗi khi cập nhật LO:', error);
        res.status(500).json({ message: 'Lỗi server', error: error.message });
    }
};

exports.deleteLO = async (req, res) => {
    const transaction = await sequelize.transaction();
    try {
        const { id } = req.params;

        if (!id) {
            await transaction.rollback();
            return res.status(400).json({ message: 'Thiếu ID của LO' });
        }

        const lo = await LO.findByPk(id, { transaction });
        if (!lo) {
            await transaction.rollback();
            return res.status(404).json({ message: 'Không tìm thấy LO' });
        }

        // Kiểm tra xem LO có đang được sử dụng trong các mối quan hệ không
        const hasQuestions = await Question.findOne({
            where: { lo_id: id },
            transaction
        });

        const hasChapters = await ChapterLO.findOne({
            where: { lo_id: id },
            transaction
        });

        if (hasQuestions || hasChapters) {
            await transaction.rollback();
            return res.status(400).json({
                message: 'Không thể xóa LO vì đang được sử dụng trong câu hỏi hoặc chương'
            });
        }

        await lo.destroy({ transaction });
        await transaction.commit();
        res.status(200).json({ message: 'Xóa LO thành công' });
    } catch (error) {
        await transaction.rollback();
        console.error('Lỗi khi xóa LO:', error);
        res.status(500).json({ message: 'Lỗi server', error: error.message });
    }
};

exports.getLOsBySubject = async (req, res) => {
    try {
        const { subjectId } = req.params;
        const { page = 1, limit = 10 } = req.query;
        const offset = (page - 1) * limit;

        if (!subjectId) {
            return res.status(400).json({ message: 'Thiếu ID của môn học' });
        }

        // Kiểm tra subject có tồn tại không
        const subject = await Subject.findByPk(subjectId);
        if (!subject) {
            return res.status(404).json({ message: 'Không tìm thấy môn học' });
        }

        // 1. Lấy danh sách chapter theo subject
        const chapters = await Chapter.findAll({
            where: { subject_id: subjectId },
            attributes: ['chapter_id'],
        });

        if (!chapters.length) {
            return res.status(404).json({ message: 'Không tìm thấy chương nào thuộc môn học này' });
        }

        const chapterIds = chapters.map(ch => ch.chapter_id);

        // 2. Lấy danh sách ChapterLO theo các chapter_id
        const chapterLOs = await ChapterLO.findAll({
            where: { chapter_id: { [Op.in]: chapterIds } },
            attributes: ['lo_id'],
        });

        if (!chapterLOs.length) {
            return res.status(404).json({ message: 'Không tìm thấy LO nào liên kết với môn học này' });
        }

        const loIds = [...new Set(chapterLOs.map(clo => clo.lo_id))];

        // 3. Lấy danh sách LO với pagination
        const { count, rows: los } = await LO.findAndCountAll({
            where: { lo_id: { [Op.in]: loIds } },
            include: [
                {
                    model: Chapter,
                    as: 'Chapters',
                    attributes: ['chapter_id', 'name'],
                    through: { attributes: [] },
                },
            ],
            limit: parseInt(limit),
            offset: parseInt(offset),
        });

        return res.status(200).json({
            totalItems: count,
            totalPages: Math.ceil(count / limit),
            currentPage: parseInt(page),
            los,
        });
    } catch (error) {
        console.error('Lỗi khi lấy LO theo Subject:', error);
        return res.status(500).json({ message: 'Lỗi server', error: error.message });
    }
};