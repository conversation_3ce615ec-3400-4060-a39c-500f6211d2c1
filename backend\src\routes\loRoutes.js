const express = require('express');
const router = express.Router();
const loController = require('../controllers/loController');

// Các route GET cần route cụ thể trước route động
router.get('/subject/:subjectId', loController.getLOsBySubject);
router.get('/', loController.getAllLOs);
router.get('/:id', loController.getLOById);

router.post('/', loController.createLO);
router.put('/:id', loController.updateLO);
router.delete('/:id', loController.deleteLO);

module.exports = router;
